<template>
  <div id="cesiumContainer" class="cesium-container"></div>
</template>

<script lang="ts" setup>
import {
  Cartesian3,
  Ion,
  Math as CesiumMath,
  SceneMode,
  Viewer,
  WebMercatorProjection
} from 'cesium'
import 'cesium/Build/Cesium/Widgets/widgets.css'
import { onMounted, onUnmounted } from 'vue'

// 设置Cesium Ion访问令牌
Ion.defaultAccessToken =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI4MWFjNTY5ZC03ZGU0LTQwZjAtODBmNi05ZjQ4MGM4YzgyMDYiLCJpZCI6MTk3ODA0LCJpYXQiOjE3NTM5NDgwNDF9.Trh3Rr_1gX3oh-ERhIbFrUGvVYwDJCMP1b16r6uv80E'

let viewer: Viewer | null = null

onMounted(() => {
  // 创建Cesium Viewer实例，配置为2D平面地图模式
  viewer = new Viewer('cesiumContainer', {
    // 设置为2D模式（平面地图）
    sceneMode: SceneMode.SCENE2D,

    // 地图投影方式，使用Web墨卡托投影（常用于平面地图）
    mapProjection: new WebMercatorProjection(),

    // 禁用3D相关的控件和功能
    scene3DOnly: false,

    // 界面配置
    animation: false,           // 隐藏动画控件
    baseLayerPicker: true,      // 显示底图选择器
    fullscreenButton: true,     // 显示全屏按钮
    geocoder: true,             // 显示地理编码搜索框
    homeButton: true,           // 显示主页按钮
    infoBox: true,              // 显示信息框
    sceneModePicker: true,      // 显示场景模式选择器（2D/3D切换）
    selectionIndicator: true,   // 显示选择指示器
    timeline: false,            // 隐藏时间轴
    navigationHelpButton: true, // 显示导航帮助按钮
    navigationInstructionsInitiallyVisible: false,

    // 地形配置
    terrainProvider: undefined, // 不使用地形数据，保持平面

    // 其他配置
    orderIndependentTranslucency: false,
    contextOptions: {
      requestWebgl1: false
    }
  })

  // 设置初始视图位置（以北京为中心）
  viewer.camera.setView({
    destination: Cartesian3.fromDegrees(116.4074, 39.9042, 10000000), // 经度、纬度、高度
    orientation: {
      heading: CesiumMath.toRadians(0.0),   // 方向角
      pitch: CesiumMath.toRadians(-90.0),   // 俯仰角（-90度为俯视）
      roll: 0.0                             // 翻滚角
    }
  })

  // 禁用地下模式
  viewer.scene.globe.translucency.enabled = false

  // 设置地图的最小和最大缩放级别
  viewer.scene.screenSpaceCameraController.minimumZoomDistance = 1000
  viewer.scene.screenSpaceCameraController.maximumZoomDistance = 50000000

  console.log('Cesium 2D平面地图初始化完成')
})

onUnmounted(() => {
  // 清理资源
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
})
</script>

<style scoped>
.cesium-container {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* 自定义Cesium控件样式 */
:deep(.cesium-viewer) {
  width: 100%;
  height: 100%;
}

:deep(.cesium-viewer-cesiumWidgetContainer) {
  width: 100%;
  height: 100%;
}

/* 隐藏Cesium logo（如果需要） */
:deep(.cesium-credit-logoContainer) {
  display: none !important;
}

/* 调整底部信息栏样式 */
:deep(.cesium-widget-credits) {
  position: absolute;
  bottom: 0;
  left: 0;
  color: #48b;
  font-size: 10px;
  background: rgba(42, 42, 42, 0.8);
  padding: 2px 5px;
  border-radius: 3px;
}
</style>
