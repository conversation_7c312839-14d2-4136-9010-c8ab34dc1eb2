# Cesium 平面地图

这是一个使用 Cesium 创建的 2D 平面地图组件。

## 功能特性

- **2D 平面地图模式**：使用 `SceneMode.SCENE2D` 创建平面地图视图
- **Web 墨卡托投影**：使用标准的 Web 墨卡托投影系统
- **交互控件**：
  - 底图选择器：可以切换不同的底图
  - 地理编码搜索：支持地址搜索定位
  - 场景模式切换：可以在 2D/3D 模式间切换
  - 导航控件：缩放、平移等操作
- **自定义样式**：优化了界面显示效果

## 访问方式

在浏览器中访问：`http://localhost:8080/cesium`

## 技术实现

### 核心配置

```typescript
// 创建 2D 平面地图
viewer = new Viewer('cesiumContainer', {
  sceneMode: SceneMode.SCENE2D,           // 2D 模式
  mapProjection: new WebMercatorProjection(), // Web 墨卡托投影
  scene3DOnly: false,                     // 允许 2D/3D 切换
  // ... 其他配置
})
```

### 视图设置

```typescript
// 设置初始视图（以北京为中心）
viewer.camera.setView({
  destination: Cartesian3.fromDegrees(116.4074, 39.9042, 10000000),
  orientation: {
    heading: CesiumMath.toRadians(0.0),
    pitch: CesiumMath.toRadians(-90.0),   // 俯视角度
    roll: 0.0
  }
})
```

## 自定义功能

可以在此基础上添加：

1. **标记点**：在地图上添加自定义标记
2. **图层管理**：添加不同的数据图层
3. **测量工具**：距离、面积测量
4. **绘制功能**：绘制点、线、面
5. **数据可视化**：热力图、聚合等

## 注意事项

1. 确保 Cesium Ion Token 有效
2. 网络连接正常（需要加载在线地图数据）
3. 浏览器支持 WebGL
