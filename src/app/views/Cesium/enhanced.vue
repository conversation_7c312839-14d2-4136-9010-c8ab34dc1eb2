<template>
  <div class="cesium-enhanced-container">
    <!-- 控制面板 -->
    <div class="control-panel">
      <div class="panel-section">
        <h3>地图控制</h3>
        <button @click="switchTo2D" :class="{ active: is2D }">2D 平面</button>
        <button @click="switchTo3D" :class="{ active: !is2D }">3D 球体</button>
      </div>
      
      <div class="panel-section">
        <h3>添加标记</h3>
        <button @click="addMarker">添加标记点</button>
        <button @click="clearMarkers">清除标记</button>
      </div>
      
      <div class="panel-section">
        <h3>视图控制</h3>
        <button @click="flyToBeijing">飞到北京</button>
        <button @click="flyToShanghai">飞到上海</button>
        <button @click="flyToGuangzhou">飞到广州</button>
      </div>
    </div>

    <!-- 地图容器 -->
    <div id="cesiumEnhancedContainer" class="cesium-map"></div>
  </div>
</template>

<script lang="ts" setup>
import {
  Cartesian3,
  Color,
  Ion,
  Math as CesiumMath,
  SceneMode,
  Viewer,
  WebMercatorProjection,
  Entity,
  PinBuilder,
  VerticalOrigin
} from 'cesium'
import 'cesium/Build/Cesium/Widgets/widgets.css'
import { onMounted, onUnmounted, ref } from 'vue'

// 设置Cesium Ion访问令牌
Ion.defaultAccessToken =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI4MWFjNTY5ZC03ZGU0LTQwZjAtODBmNi05ZjQ4MGM4YzgyMDYiLCJpZCI6MTk3ODA0LCJpYXQiOjE3NTM5NDgwNDF9.Trh3Rr_1gX3oh-ERhIbFrUGvVYwDJCMP1b16r6uv80E'

let viewer: Viewer | null = null
const is2D = ref(true)
const markers: Entity[] = []

// 城市坐标
const cities = {
  beijing: { lng: 116.4074, lat: 39.9042, name: '北京' },
  shanghai: { lng: 121.4737, lat: 31.2304, name: '上海' },
  guangzhou: { lng: 113.2644, lat: 23.1291, name: '广州' }
}

onMounted(() => {
  // 创建Cesium Viewer实例
  viewer = new Viewer('cesiumEnhancedContainer', {
    sceneMode: SceneMode.SCENE2D,
    mapProjection: new WebMercatorProjection(),
    scene3DOnly: false,
    animation: false,
    baseLayerPicker: true,
    fullscreenButton: true,
    geocoder: true,
    homeButton: true,
    infoBox: true,
    sceneModePicker: false, // 我们用自定义按钮控制
    selectionIndicator: true,
    timeline: false,
    navigationHelpButton: true,
    navigationInstructionsInitiallyVisible: false,
    terrainProvider: undefined,
    orderIndependentTranslucency: false
  })

  // 设置初始视图
  viewer.camera.setView({
    destination: Cartesian3.fromDegrees(116.4074, 39.9042, 5000000),
    orientation: {
      heading: CesiumMath.toRadians(0.0),
      pitch: CesiumMath.toRadians(-90.0),
      roll: 0.0
    }
  })

  // 禁用地下模式
  viewer.scene.globe.translucency.enabled = false
  
  // 设置缩放限制
  viewer.scene.screenSpaceCameraController.minimumZoomDistance = 1000
  viewer.scene.screenSpaceCameraController.maximumZoomDistance = 50000000

  // 添加点击事件监听器
  viewer.cesiumWidget.screenSpaceEventHandler.setInputAction((event: any) => {
    const pickedPosition = viewer!.camera.pickEllipsoid(event.position, viewer!.scene.globe.ellipsoid)
    if (pickedPosition) {
      const cartographic = viewer!.scene.globe.ellipsoid.cartesianToCartographic(pickedPosition)
      const longitude = CesiumMath.toDegrees(cartographic.longitude)
      const latitude = CesiumMath.toDegrees(cartographic.latitude)
      console.log(`点击位置: 经度 ${longitude.toFixed(6)}, 纬度 ${latitude.toFixed(6)}`)
    }
  }, 1) // LEFT_CLICK

  console.log('增强版 Cesium 2D 平面地图初始化完成')
})

// 切换到2D模式
const switchTo2D = () => {
  if (viewer) {
    viewer.scene.mode = SceneMode.SCENE2D
    is2D.value = true
  }
}

// 切换到3D模式
const switchTo3D = () => {
  if (viewer) {
    viewer.scene.mode = SceneMode.SCENE3D
    is2D.value = false
  }
}

// 添加标记点
const addMarker = () => {
  if (!viewer) return
  
  const pinBuilder = new PinBuilder()
  const pin = pinBuilder.fromText(`${markers.length + 1}`, Color.YELLOW, 48)
  
  // 在随机位置添加标记
  const longitude = 110 + Math.random() * 10
  const latitude = 30 + Math.random() * 10
  
  const entity = viewer.entities.add({
    position: Cartesian3.fromDegrees(longitude, latitude),
    billboard: {
      image: pin,
      verticalOrigin: VerticalOrigin.BOTTOM
    },
    label: {
      text: `标记点 ${markers.length + 1}`,
      font: '14pt sans-serif',
      pixelOffset: new Cartesian3(0, -50, 0),
      fillColor: Color.WHITE,
      outlineColor: Color.BLACK,
      outlineWidth: 2,
      style: 1 // FILL_AND_OUTLINE
    }
  })
  
  markers.push(entity)
}

// 清除所有标记
const clearMarkers = () => {
  if (!viewer) return
  
  markers.forEach(marker => {
    viewer!.entities.remove(marker)
  })
  markers.length = 0
}

// 飞到指定城市
const flyToCity = (city: { lng: number; lat: number; name: string }) => {
  if (!viewer) return
  
  viewer.camera.flyTo({
    destination: Cartesian3.fromDegrees(city.lng, city.lat, 1000000),
    orientation: {
      heading: CesiumMath.toRadians(0.0),
      pitch: CesiumMath.toRadians(-90.0),
      roll: 0.0
    },
    duration: 2.0
  })
}

const flyToBeijing = () => flyToCity(cities.beijing)
const flyToShanghai = () => flyToCity(cities.shanghai)
const flyToGuangzhou = () => flyToCity(cities.guangzhou)

onUnmounted(() => {
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
})
</script>

<style scoped>
.cesium-enhanced-container {
  display: flex;
  width: 100%;
  height: 100vh;
  position: relative;
}

.control-panel {
  width: 250px;
  background: rgba(48, 48, 48, 0.9);
  color: white;
  padding: 20px;
  overflow-y: auto;
  z-index: 1000;
}

.panel-section {
  margin-bottom: 20px;
}

.panel-section h3 {
  margin: 0 0 10px 0;
  color: #48b;
  font-size: 14px;
  border-bottom: 1px solid #48b;
  padding-bottom: 5px;
}

.panel-section button {
  display: block;
  width: 100%;
  margin: 5px 0;
  padding: 8px 12px;
  background: #333;
  color: white;
  border: 1px solid #555;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s;
}

.panel-section button:hover {
  background: #555;
  border-color: #48b;
}

.panel-section button.active {
  background: #48b;
  border-color: #48b;
}

.cesium-map {
  flex: 1;
  height: 100%;
}

/* Cesium 样式覆盖 */
:deep(.cesium-viewer) {
  width: 100%;
  height: 100%;
}

:deep(.cesium-viewer-cesiumWidgetContainer) {
  width: 100%;
  height: 100%;
}

:deep(.cesium-credit-logoContainer) {
  display: none !important;
}

:deep(.cesium-widget-credits) {
  position: absolute;
  bottom: 0;
  left: 0;
  color: #48b;
  font-size: 10px;
  background: rgba(42, 42, 42, 0.8);
  padding: 2px 5px;
  border-radius: 3px;
}
</style>
