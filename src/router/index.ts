import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  // history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/cesium',
      name: 'Cesium',
      component: () => import('@/app/views/Cesium/index.vue')
    },
    {
      path: '/cesium-enhanced',
      name: 'CesiumEnhanced',
      component: () => import('@/app/views/Cesium/enhanced.vue')
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login/index.vue')
    },
    {
      path: '/ResetPassword',
      name: 'ResetPassword',
      component: () => import('@/views/ResetPassword/index.vue')
    },
    {
      path: '/:catchAll(.*)',
      name: 'Home',
      component: () => import('@/views/Home/index.vue'),
      // redirect: '/user/manage',
      children: [
        // {
        //   path: '/user/manage',
        //   name: 'UserManage',
        //   component: () => import('@/views/UserManage/index.vue')
        // }
      ]
    }
    // {
    //   path: '/:catchAll(.*)',
    //   name: 'NotFound',
    //   component: () => import('@/views/NotFound/index.vue')
    // }
  ]
})

router.beforeEach((to, from, next) => {
  const filterRouteName: string[] = ['NotFound', 'Login', 'Cesium', 'CesiumEnhanced']
  // console.log('to, from, :>> ', to, from)
  const access_token = localStorage.getItem('access_token')
  // console.log('access_token :>> ', access_token)
  if (filterRouteName.includes(to.name as string)) {
    next()
  } else {
    // 需要登录的路由，进行 token 判断
    if (access_token) {
      // token 存在，可以继续导航
      next()
    } else {
      // token 不存在，重定向到登录页
      next('/login')
    }
  }
})
export default router
