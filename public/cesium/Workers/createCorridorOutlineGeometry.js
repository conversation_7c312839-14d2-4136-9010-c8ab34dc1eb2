/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.131
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as C}from"./chunk-SRJEQ4WA.js";import{a as Q}from"./chunk-PQSWZ6QE.js";import"./chunk-RQ3X5FSK.js";import"./chunk-KORWG3CG.js";import{a as J}from"./chunk-AOSK7NDA.js";import"./chunk-BGQDOF2W.js";import"./chunk-SPVHGWGM.js";import{b as K}from"./chunk-6EFYFFBI.js";import{a as it}from"./chunk-I3XHKING.js";import"./chunk-N6G72L23.js";import"./chunk-HLFNWOQ4.js";import"./chunk-YDUNCXSU.js";import{a as X}from"./chunk-LNFOIPBT.js";import{a as et}from"./chunk-7IBY6LYM.js";import{b as I,c as tt,d as z}from"./chunk-BDLVVHOG.js";import{d as x}from"./chunk-VMOPYKP4.js";import"./chunk-DGGIK7AD.js";import{a as q}from"./chunk-5EZKANRV.js";import{a as A,d as P,f as $}from"./chunk-FCVV6A2F.js";import{a as Y}from"./chunk-PO64KPKA.js";import"./chunk-JOXXXMPE.js";import"./chunk-7RJW35V3.js";import{b as B}from"./chunk-R3KD5ZAI.js";import{e as N}from"./chunk-GBI3IATP.js";var ot=new A,nt=new A,lt=new A;function ft(t,i){for(let e=0;e<t.length;e++)t[e]=i.scaleToGeodeticSurface(t[e],t[e]);return t}function st(t,i){let e=[],r=t.positions,h=t.corners,m=t.endPositions,H=new et,y,u=0,p=0,o,g=0,d;for(o=0;o<r.length;o+=2)d=r[o].length-3,u+=d,g+=d/3*4,p+=r[o+1].length-3;for(u+=3,p+=3,o=0;o<h.length;o++){y=h[o];let c=h[o].leftPositions;N(c)?(d=c.length,u+=d,g+=d/3*2):(d=h[o].rightPositions.length,p+=d,g+=d/3*2)}let _=N(m),w;_&&(w=m[0].length-3,u+=w,p+=w,w/=3,g+=w*4);let T=u+p,b=new Float64Array(T),n=0,s=T-1,a,E,L,k,S,U,j=w/2,l=X.createTypedArray(T/3,g+4),f=0;if(l[f++]=n/3,l[f++]=(s-2)/3,_){e.push(n/3),U=ot,S=nt;let c=m[0];for(o=0;o<j;o++)U=A.fromArray(c,(j-1-o)*3,U),S=A.fromArray(c,(j+o)*3,S),C.addAttribute(b,S,n),C.addAttribute(b,U,void 0,s),E=n/3,k=E+1,a=(s-2)/3,L=a-1,l[f++]=a,l[f++]=L,l[f++]=E,l[f++]=k,n+=3,s-=3}let V=0,F=r[V++],D=r[V++];for(b.set(F,n),b.set(D,s-D.length+1),d=D.length-3,e.push(n/3,(s-2)/3),o=0;o<d;o+=3)E=n/3,k=E+1,a=(s-2)/3,L=a-1,l[f++]=a,l[f++]=L,l[f++]=E,l[f++]=k,n+=3,s-=3;for(o=0;o<h.length;o++){let c;y=h[o];let G=y.leftPositions,W=y.rightPositions,O,R=lt;if(N(G)){for(s-=3,O=L,e.push(k),c=0;c<G.length/3;c++)R=A.fromArray(G,c*3,R),l[f++]=O-c-1,l[f++]=O-c,C.addAttribute(b,R,void 0,s),s-=3;e.push(O-Math.floor(G.length/6)),i===Q.BEVELED&&e.push((s-2)/3+1),n+=3}else{for(n+=3,O=k,e.push(L),c=0;c<W.length/3;c++)R=A.fromArray(W,c*3,R),l[f++]=O+c,l[f++]=O+c+1,C.addAttribute(b,R,n),n+=3;e.push(O+Math.floor(W.length/6)),i===Q.BEVELED&&e.push(n/3-1),s-=3}for(F=r[V++],D=r[V++],F.splice(0,3),D.splice(D.length-3,3),b.set(F,n),b.set(D,s-D.length+1),d=D.length-3,c=0;c<D.length;c+=3)k=n/3,E=k-1,L=(s-2)/3,a=L+1,l[f++]=a,l[f++]=L,l[f++]=E,l[f++]=k,n+=3,s-=3;n-=3,s+=3,e.push(n/3,(s-2)/3)}if(_){n+=3,s-=3,U=ot,S=nt;let c=m[1];for(o=0;o<j;o++)U=A.fromArray(c,(w-o-1)*3,U),S=A.fromArray(c,o*3,S),C.addAttribute(b,U,void 0,s),C.addAttribute(b,S,n),k=n/3,E=k-1,L=(s-2)/3,a=L+1,l[f++]=a,l[f++]=L,l[f++]=E,l[f++]=k,n+=3,s-=3;e.push(n/3)}else e.push(n/3,(s-2)/3);return l[f++]=n/3,l[f++]=(s-2)/3,H.position=new z({componentDatatype:q.DOUBLE,componentsPerAttribute:3,values:b}),{attributes:H,indices:l,wallIndices:e}}function ct(t){let i=t.ellipsoid,e=C.computePositions(t),r=st(e,t.cornerType),h=r.wallIndices,m=t.height,H=t.extrudedHeight,y=r.attributes,u=r.indices,p=y.position.values,o=p.length,g=new Float64Array(o);g.set(p);let d=new Float64Array(o*2);if(p=K.scaleToGeodeticHeight(p,m,i),g=K.scaleToGeodeticHeight(g,H,i),d.set(p),d.set(g,o),y.position.values=d,o/=3,N(t.offsetAttribute)){let a=new Uint8Array(o*2);if(t.offsetAttribute===J.TOP)a=a.fill(1,0,o);else{let E=t.offsetAttribute===J.NONE?0:1;a=a.fill(E)}y.applyOffset=new z({componentDatatype:q.UNSIGNED_BYTE,componentsPerAttribute:1,values:a})}let _,w=u.length,T=X.createTypedArray(d.length/3,(w+h.length)*2);T.set(u);let b=w;for(_=0;_<w;_+=2){let a=u[_],E=u[_+1];T[b++]=a+o,T[b++]=E+o}let n,s;for(_=0;_<h.length;_++)n=h[_],s=n+o,T[b++]=n,T[b++]=s;return{attributes:y,indices:T}}function M(t){t=t??$.EMPTY_OBJECT;let i=t.positions,e=t.width;B.typeOf.object("options.positions",i),B.typeOf.number("options.width",e);let r=t.height??0,h=t.extrudedHeight??r;this._positions=i,this._ellipsoid=P.clone(t.ellipsoid??P.default),this._width=e,this._height=Math.max(r,h),this._extrudedHeight=Math.min(r,h),this._cornerType=t.cornerType??Q.ROUNDED,this._granularity=t.granularity??Y.RADIANS_PER_DEGREE,this._offsetAttribute=t.offsetAttribute,this._workerName="createCorridorOutlineGeometry",this.packedLength=1+i.length*A.packedLength+P.packedLength+6}M.pack=function(t,i,e){B.typeOf.object("value",t),B.typeOf.object("array",i),e=e??0;let r=t._positions,h=r.length;i[e++]=h;for(let m=0;m<h;++m,e+=A.packedLength)A.pack(r[m],i,e);return P.pack(t._ellipsoid,i,e),e+=P.packedLength,i[e++]=t._width,i[e++]=t._height,i[e++]=t._extrudedHeight,i[e++]=t._cornerType,i[e++]=t._granularity,i[e]=t._offsetAttribute??-1,i};var rt=P.clone(P.UNIT_SPHERE),v={positions:void 0,ellipsoid:rt,width:void 0,height:void 0,extrudedHeight:void 0,cornerType:void 0,granularity:void 0,offsetAttribute:void 0};M.unpack=function(t,i,e){B.typeOf.object("array",t),i=i??0;let r=t[i++],h=new Array(r);for(let d=0;d<r;++d,i+=A.packedLength)h[d]=A.unpack(t,i);let m=P.unpack(t,i,rt);i+=P.packedLength;let H=t[i++],y=t[i++],u=t[i++],p=t[i++],o=t[i++],g=t[i];return N(e)?(e._positions=h,e._ellipsoid=P.clone(m,e._ellipsoid),e._width=H,e._height=y,e._extrudedHeight=u,e._cornerType=p,e._granularity=o,e._offsetAttribute=g===-1?void 0:g,e):(v.positions=h,v.width=H,v.height=y,v.extrudedHeight=u,v.cornerType=p,v.granularity=o,v.offsetAttribute=g===-1?void 0:g,new M(v))};M.createGeometry=function(t){let i=t._positions,e=t._width,r=t._ellipsoid;i=ft(i,r);let h=it(i,A.equalsEpsilon);if(h.length<2||e<=0)return;let m=t._height,H=t._extrudedHeight,y=!Y.equalsEpsilon(m,H,0,Y.EPSILON2),u={ellipsoid:r,positions:h,width:e,cornerType:t._cornerType,granularity:t._granularity,saveAttributes:!1},p;if(y)u.height=m,u.extrudedHeight=H,u.offsetAttribute=t._offsetAttribute,p=ct(u);else{let d=C.computePositions(u);if(p=st(d,u.cornerType),p.attributes.position.values=K.scaleToGeodeticHeight(p.attributes.position.values,m,r),N(t._offsetAttribute)){let _=p.attributes.position.values.length,w=t._offsetAttribute===J.NONE?0:1,T=new Uint8Array(_/3).fill(w);p.attributes.applyOffset=new z({componentDatatype:q.UNSIGNED_BYTE,componentsPerAttribute:1,values:T})}}let o=p.attributes,g=x.fromVertices(o.position.values,void 0,3);return new tt({attributes:o,indices:p.indices,primitiveType:I.LINES,boundingSphere:g,offsetAttribute:t._offsetAttribute})};var Z=M;function ht(t,i){return N(i)&&(t=Z.unpack(t,i)),t._ellipsoid=P.clone(t._ellipsoid),Z.createGeometry(t)}var Ot=ht;export{Ot as default};
